{"version": 3, "names": ["_classApplyDescriptorGet", "require", "_classPrivateFieldGet2", "_classPrivateFieldGet", "receiver", "privateMap", "descriptor", "classPrivateFieldGet2", "classApplyDescriptorGet"], "sources": ["../../src/helpers/classPrivateFieldGet.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n/* @onlyBabel7 */\n\nimport classApplyDescriptorGet from \"classApplyDescriptorGet\";\nimport classPrivateFieldGet2 from \"classPrivateFieldGet2\";\nexport default function _classPrivateFieldGet(receiver, privateMap) {\n  var descriptor = classPrivateFieldGet2(privateMap, receiver);\n  return classApplyDescriptorGet(receiver, descriptor);\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,wBAAA,GAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AACe,SAASE,qBAAqBA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAClE,IAAIC,UAAU,GAAGC,sBAAqB,CAACF,UAAU,EAAED,QAAQ,CAAC;EAC5D,OAAOI,wBAAuB,CAACJ,QAAQ,EAAEE,UAAU,CAAC;AACtD", "ignoreList": []}