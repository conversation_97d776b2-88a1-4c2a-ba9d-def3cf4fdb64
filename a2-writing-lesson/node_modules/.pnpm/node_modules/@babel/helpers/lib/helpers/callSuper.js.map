{"version": 3, "names": ["_getPrototypeOf", "require", "_isNativeReflectConstruct", "_possibleConstructorReturn", "_callSuper", "_this", "derived", "args", "getPrototypeOf", "possibleConstructorReturn", "isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply"], "sources": ["../../src/helpers/callSuper.ts"], "sourcesContent": ["/* @minVersion 7.23.8 */\n\n// This is duplicated to packages/babel-plugin-transform-classes/src/inline-callSuper-helpers.ts\n\nimport getPrototypeOf from \"./getPrototypeOf.ts\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.ts\";\nimport possibleConstructorReturn from \"./possibleConstructorReturn.ts\";\n\nexport default function _callSuper(\n  _this: object,\n  derived: Function,\n  args: ArrayLike<any>,\n) {\n  // Super\n  derived = getPrototypeOf(derived);\n  return possibleConstructorReturn(\n    _this,\n    isNativeReflectConstruct()\n      ? // NOTE: This doesn't work if this.__proto__.constructor has been modified.\n        Reflect.construct(\n          derived,\n          args || [],\n          getPrototypeOf(_this).constructor,\n        )\n      : derived.apply(_this, args),\n  );\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,yBAAA,GAAAD,OAAA;AACA,IAAAE,0BAAA,GAAAF,OAAA;AAEe,SAASG,UAAUA,CAChCC,KAAa,EACbC,OAAiB,EACjBC,IAAoB,EACpB;EAEAD,OAAO,GAAG,IAAAE,uBAAc,EAACF,OAAO,CAAC;EACjC,OAAO,IAAAG,kCAAyB,EAC9BJ,KAAK,EACL,IAAAK,iCAAwB,EAAC,CAAC,GAEtBC,OAAO,CAACC,SAAS,CACfN,OAAO,EACPC,IAAI,IAAI,EAAE,EACV,IAAAC,uBAAc,EAACH,KAAK,CAAC,CAACQ,WACxB,CAAC,GACDP,OAAO,CAACQ,KAAK,CAACT,KAAK,EAAEE,IAAI,CAC/B,CAAC;AACH", "ignoreList": []}