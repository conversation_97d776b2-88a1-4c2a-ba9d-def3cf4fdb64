{"version": 3, "names": ["_arrayLikeToArray", "arr", "len", "length", "i", "arr2", "Array"], "sources": ["../../src/helpers/arrayLikeToArray.ts"], "sourcesContent": ["/* @minVersion 7.9.0 */\n\nexport default function _arrayLikeToArray<T>(\n  arr: ArrayLike<T>,\n  len?: number | null,\n) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array<T>(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,iBAAiBA,CACvCC,GAAiB,EACjBC,GAAmB,EACnB;EACA,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAACE,MAAM,EAAED,GAAG,GAAGD,GAAG,CAACE,MAAM;EACrD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAAIJ,GAAG,CAAC,EAAEE,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAEC,IAAI,CAACD,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,CAAC;EACxE,OAAOC,IAAI;AACb", "ignoreList": []}