{"version": 3, "file": "index.js", "sources": ["../src/defs.ts", "../src/highlight.ts", "../src/index.ts"], "sourcesContent": ["import picocolors, { createColors } from \"picocolors\";\nimport type { Colors, Formatter } from \"picocolors/types\";\n\nexport function isColorSupported() {\n  return (\n    // See https://github.com/alexeyraspopov/picocolors/issues/62\n    typeof process === \"object\" &&\n      (process.env.FORCE_COLOR === \"0\" || process.env.FORCE_COLOR === \"false\")\n      ? false\n      : picocolors.isColorSupported\n  );\n}\n\nexport type InternalTokenType =\n  | \"keyword\"\n  | \"capitalized\"\n  | \"jsxIdentifier\"\n  | \"punctuator\"\n  | \"number\"\n  | \"string\"\n  | \"regex\"\n  | \"comment\"\n  | \"invalid\";\n\ntype UITokens = \"gutter\" | \"marker\" | \"message\";\n\nexport type Defs = {\n  [_ in InternalTokenType | UITokens | \"reset\"]: Formatter;\n};\n\nconst compose: <T, U, V>(f: (gv: U) => V, g: (v: T) => U) => (v: T) => V =\n  (f, g) => v =>\n    f(g(v));\n\n/**\n * Styles for token types.\n */\nfunction buildDefs(colors: Colors): Defs {\n  return {\n    keyword: colors.cyan,\n    capitalized: colors.yellow,\n    jsxIdentifier: colors.yellow,\n    punctuator: colors.yellow,\n    number: colors.magenta,\n    string: colors.green,\n    regex: colors.magenta,\n    comment: colors.gray,\n    invalid: compose(compose(colors.white, colors.bgRed), colors.bold),\n\n    gutter: colors.gray,\n    marker: compose(colors.red, colors.bold),\n    message: compose(colors.red, colors.bold),\n\n    reset: colors.reset,\n  };\n}\n\nconst defsOn = buildDefs(createColors(true));\nconst defsOff = buildDefs(createColors(false));\n\nexport function getDefs(enabled: boolean): Defs {\n  return enabled ? defsOn : defsOff;\n}\n", "import type { Token as JSToken, JSXToken } from \"js-tokens\";\nimport jsTokens from \"js-tokens\";\n\nimport {\n  isStrictReservedWord,\n  isKeyword,\n} from \"@babel/helper-validator-identifier\";\n\nimport { getDefs, type InternalTokenType } from \"./defs.ts\";\n\n/**\n * Names that are always allowed as identifiers, but also appear as keywords\n * within certain syntactic productions.\n *\n * https://tc39.es/ecma262/#sec-keywords-and-reserved-words\n *\n * `target` has been omitted since it is very likely going to be a false\n * positive.\n */\nconst sometimesKeywords = new Set([\"as\", \"async\", \"from\", \"get\", \"of\", \"set\"]);\n\ntype Token = {\n  type: InternalTokenType | \"uncolored\";\n  value: string;\n};\n\n/**\n * RegExp to test for newlines in terminal.\n */\nconst NEWLINE = /\\r\\n|[\\n\\r\\u2028\\u2029]/;\n\n/**\n * RegExp to test for the three types of brackets.\n */\nconst BRACKET = /^[()[\\]{}]$/;\n\nlet tokenize: (\n  text: string,\n) => Generator<{ type: InternalTokenType | \"uncolored\"; value: string }>;\n\nif (process.env.BABEL_8_BREAKING) {\n  /**\n   * Get the type of token, specifying punctuator type.\n   */\n  const getTokenType = function (\n    token: JSToken | JSXToken,\n  ): InternalTokenType | \"uncolored\" {\n    if (token.type === \"IdentifierName\") {\n      if (\n        isKeyword(token.value) ||\n        isStrictReservedWord(token.value, true) ||\n        sometimesKeywords.has(token.value)\n      ) {\n        return \"keyword\";\n      }\n\n      if (token.value[0] !== token.value[0].toLowerCase()) {\n        return \"capitalized\";\n      }\n    }\n\n    if (token.type === \"Punctuator\" && BRACKET.test(token.value)) {\n      return \"uncolored\";\n    }\n\n    if (token.type === \"Invalid\" && token.value === \"@\") {\n      return \"punctuator\";\n    }\n\n    switch (token.type) {\n      case \"NumericLiteral\":\n        return \"number\";\n\n      case \"StringLiteral\":\n      case \"JSXString\":\n      case \"NoSubstitutionTemplate\":\n        return \"string\";\n\n      case \"RegularExpressionLiteral\":\n        return \"regex\";\n\n      case \"Punctuator\":\n      case \"JSXPunctuator\":\n        return \"punctuator\";\n\n      case \"MultiLineComment\":\n      case \"SingleLineComment\":\n        return \"comment\";\n\n      case \"Invalid\":\n      case \"JSXInvalid\":\n        return \"invalid\";\n\n      case \"JSXIdentifier\":\n        return \"jsxIdentifier\";\n\n      default:\n        return \"uncolored\";\n    }\n  };\n\n  /**\n   * Turn a string of JS into an array of objects.\n   */\n  tokenize = function* (text: string): Generator<Token> {\n    for (const token of jsTokens(text, { jsx: true })) {\n      switch (token.type) {\n        case \"TemplateHead\":\n          yield { type: \"string\", value: token.value.slice(0, -2) };\n          yield { type: \"punctuator\", value: \"${\" };\n          break;\n\n        case \"TemplateMiddle\":\n          yield { type: \"punctuator\", value: \"}\" };\n          yield { type: \"string\", value: token.value.slice(1, -2) };\n          yield { type: \"punctuator\", value: \"${\" };\n          break;\n\n        case \"TemplateTail\":\n          yield { type: \"punctuator\", value: \"}\" };\n          yield { type: \"string\", value: token.value.slice(1) };\n          break;\n\n        default:\n          yield {\n            type: getTokenType(token),\n            value: token.value,\n          };\n      }\n    }\n  };\n} else {\n  /**\n   * RegExp to test for what seems to be a JSX tag name.\n   */\n  const JSX_TAG = /^[a-z][\\w-]*$/i;\n\n  // The token here is defined in js-tokens@4. However we don't bother\n  // typing it since the whole block will be removed in Babel 8\n  const getTokenType = function (token: any, offset: number, text: string) {\n    if (token.type === \"name\") {\n      if (\n        isKeyword(token.value) ||\n        isStrictReservedWord(token.value, true) ||\n        sometimesKeywords.has(token.value)\n      ) {\n        return \"keyword\";\n      }\n\n      if (\n        JSX_TAG.test(token.value) &&\n        (text[offset - 1] === \"<\" || text.slice(offset - 2, offset) === \"</\")\n      ) {\n        return \"jsxIdentifier\";\n      }\n\n      if (token.value[0] !== token.value[0].toLowerCase()) {\n        return \"capitalized\";\n      }\n    }\n\n    if (token.type === \"punctuator\" && BRACKET.test(token.value)) {\n      return \"bracket\";\n    }\n\n    if (\n      token.type === \"invalid\" &&\n      (token.value === \"@\" || token.value === \"#\")\n    ) {\n      return \"punctuator\";\n    }\n\n    return token.type;\n  };\n\n  tokenize = function* (text: string) {\n    let match;\n    while ((match = (jsTokens as any).default.exec(text))) {\n      const token = (jsTokens as any).matchToToken(match);\n\n      yield {\n        type: getTokenType(token, match.index, text),\n        value: token.value,\n      };\n    }\n  };\n}\n\nexport function highlight(text: string) {\n  if (text === \"\") return \"\";\n\n  const defs = getDefs(true);\n\n  let highlighted = \"\";\n\n  for (const { type, value } of tokenize(text)) {\n    if (type in defs) {\n      highlighted += value\n        .split(NEWLINE)\n        .map(str => defs[type as InternalTokenType](str))\n        .join(\"\\n\");\n    } else {\n      highlighted += value;\n    }\n  }\n\n  return highlighted;\n}\n", "import { getDefs, isColorSupported } from \"./defs.ts\";\nimport { highlight } from \"./highlight.ts\";\n\nexport { highlight };\n\nlet deprecationWarningShown = false;\n\ntype Location = {\n  column: number;\n  line: number;\n};\n\ntype NodeLocation = {\n  end?: Location;\n  start: Location;\n};\n\nexport interface Options {\n  /** Syntax highlight the code as JavaScript for terminals. default: false */\n  highlightCode?: boolean;\n  /**  The number of lines to show above the error. default: 2 */\n  linesAbove?: number;\n  /**  The number of lines to show below the error. default: 3 */\n  linesBelow?: number;\n  /**\n   * Forcibly syntax highlight the code as JavaScript (for non-terminals);\n   * overrides highlightCode.\n   * default: false\n   */\n  forceColor?: boolean;\n  /**\n   * Pass in a string to be displayed inline (if possible) next to the\n   * highlighted location in the code. If it can't be positioned inline,\n   * it will be placed above the code frame.\n   * default: nothing\n   */\n  message?: string;\n}\n\n/**\n * RegExp to test for newlines in terminal.\n */\n\nconst NEWLINE = /\\r\\n|[\\n\\r\\u2028\\u2029]/;\n\n/**\n * Extract what lines should be marked and highlighted.\n */\n\ntype MarkerLines = Record<number, true | [number, number]>;\n\nfunction getMarkerLines(\n  loc: NodeLocation,\n  source: Array<string>,\n  opts: Options,\n): {\n  start: number;\n  end: number;\n  markerLines: MarkerLines;\n} {\n  const startLoc: Location = {\n    column: 0,\n    line: -1,\n    ...loc.start,\n  };\n  const endLoc: Location = {\n    ...startLoc,\n    ...loc.end,\n  };\n  const { linesAbove = 2, linesBelow = 3 } = opts || {};\n  const startLine = startLoc.line;\n  const startColumn = startLoc.column;\n  const endLine = endLoc.line;\n  const endColumn = endLoc.column;\n\n  let start = Math.max(startLine - (linesAbove + 1), 0);\n  let end = Math.min(source.length, endLine + linesBelow);\n\n  if (startLine === -1) {\n    start = 0;\n  }\n\n  if (endLine === -1) {\n    end = source.length;\n  }\n\n  const lineDiff = endLine - startLine;\n  const markerLines: MarkerLines = {};\n\n  if (lineDiff) {\n    for (let i = 0; i <= lineDiff; i++) {\n      const lineNumber = i + startLine;\n\n      if (!startColumn) {\n        markerLines[lineNumber] = true;\n      } else if (i === 0) {\n        const sourceLength = source[lineNumber - 1].length;\n\n        markerLines[lineNumber] = [startColumn, sourceLength - startColumn + 1];\n      } else if (i === lineDiff) {\n        markerLines[lineNumber] = [0, endColumn];\n      } else {\n        const sourceLength = source[lineNumber - i].length;\n\n        markerLines[lineNumber] = [0, sourceLength];\n      }\n    }\n  } else {\n    if (startColumn === endColumn) {\n      if (startColumn) {\n        markerLines[startLine] = [startColumn, 0];\n      } else {\n        markerLines[startLine] = true;\n      }\n    } else {\n      markerLines[startLine] = [startColumn, endColumn - startColumn];\n    }\n  }\n\n  return { start, end, markerLines };\n}\n\nexport function codeFrameColumns(\n  rawLines: string,\n  loc: NodeLocation,\n  opts: Options = {},\n): string {\n  const shouldHighlight =\n    opts.forceColor || (isColorSupported() && opts.highlightCode);\n  const defs = getDefs(shouldHighlight);\n\n  const lines = rawLines.split(NEWLINE);\n  const { start, end, markerLines } = getMarkerLines(loc, lines, opts);\n  const hasColumns = loc.start && typeof loc.start.column === \"number\";\n\n  const numberMaxWidth = String(end).length;\n\n  const highlightedLines = shouldHighlight ? highlight(rawLines) : rawLines;\n\n  let frame = highlightedLines\n    .split(NEWLINE, end)\n    .slice(start, end)\n    .map((line, index) => {\n      const number = start + 1 + index;\n      const paddedNumber = ` ${number}`.slice(-numberMaxWidth);\n      const gutter = ` ${paddedNumber} |`;\n      const hasMarker = markerLines[number];\n      const lastMarkerLine = !markerLines[number + 1];\n      if (hasMarker) {\n        let markerLine = \"\";\n        if (Array.isArray(hasMarker)) {\n          const markerSpacing = line\n            .slice(0, Math.max(hasMarker[0] - 1, 0))\n            .replace(/[^\\t]/g, \" \");\n          const numberOfMarkers = hasMarker[1] || 1;\n\n          markerLine = [\n            \"\\n \",\n            defs.gutter(gutter.replace(/\\d/g, \" \")),\n            \" \",\n            markerSpacing,\n            defs.marker(\"^\").repeat(numberOfMarkers),\n          ].join(\"\");\n\n          if (lastMarkerLine && opts.message) {\n            markerLine += \" \" + defs.message(opts.message);\n          }\n        }\n        return [\n          defs.marker(\">\"),\n          defs.gutter(gutter),\n          line.length > 0 ? ` ${line}` : \"\",\n          markerLine,\n        ].join(\"\");\n      } else {\n        return ` ${defs.gutter(gutter)}${line.length > 0 ? ` ${line}` : \"\"}`;\n      }\n    })\n    .join(\"\\n\");\n\n  if (opts.message && !hasColumns) {\n    frame = `${\" \".repeat(numberMaxWidth + 1)}${opts.message}\\n${frame}`;\n  }\n\n  if (shouldHighlight) {\n    return defs.reset(frame);\n  } else {\n    return frame;\n  }\n}\n\n/**\n * Create a code frame, adding line numbers, code highlighting, and pointing to a given position.\n */\n\nexport default function (\n  rawLines: string,\n  lineNumber: number,\n  colNumber?: number | null,\n  opts: Options = {},\n): string {\n  if (!deprecationWarningShown) {\n    deprecationWarningShown = true;\n\n    const message =\n      \"Passing lineNumber and colNumber is deprecated to @babel/code-frame. Please use `codeFrameColumns`.\";\n\n    if (process.emitWarning) {\n      // A string is directly supplied to emitWarning, because when supplying an\n      // Error object node throws in the tests because of different contexts\n      process.emitWarning(message, \"DeprecationWarning\");\n    } else {\n      const deprecationError = new Error(message);\n      deprecationError.name = \"DeprecationWarning\";\n      console.warn(new Error(message));\n    }\n  }\n\n  colNumber = Math.max(colNumber, 0);\n\n  const location: NodeLocation = {\n    start: { column: colNumber, line: lineNumber },\n  };\n\n  return codeFrameColumns(rawLines, location, opts);\n}\n"], "names": ["isColorSupported", "process", "env", "FORCE_COLOR", "picocolors", "compose", "f", "g", "v", "buildDefs", "colors", "keyword", "cyan", "capitalized", "yellow", "jsxIdentifier", "punctuator", "number", "magenta", "string", "green", "regex", "comment", "gray", "invalid", "white", "bgRed", "bold", "gutter", "marker", "red", "message", "reset", "defsOn", "createColors", "defs<PERSON><PERSON>", "getDefs", "enabled", "sometimesKeywords", "Set", "NEWLINE", "BRACKET", "tokenize", "JSX_TAG", "getTokenType", "token", "offset", "text", "type", "isKeyword", "value", "isStrictReservedWord", "has", "test", "slice", "toLowerCase", "match", "jsTokens", "default", "exec", "matchToToken", "index", "highlight", "defs", "highlighted", "split", "map", "str", "join", "deprecationWarningShown", "getMarkerLines", "loc", "source", "opts", "startLoc", "Object", "assign", "column", "line", "start", "endLoc", "end", "linesAbove", "linesBelow", "startLine", "startColumn", "endLine", "endColumn", "Math", "max", "min", "length", "lineDiff", "markerLines", "i", "lineNumber", "sourceLength", "codeFrameColumns", "rawLines", "should<PERSON><PERSON><PERSON>", "forceColor", "highlightCode", "lines", "hasColumns", "numberMaxWidth", "String", "highlightedLines", "frame", "paddedNumber", "<PERSON><PERSON><PERSON><PERSON>", "lastMarkerLine", "markerLine", "Array", "isArray", "markerSpacing", "replace", "numberOfMarkers", "repeat", "colNumber", "emitWarning", "deprecationError", "Error", "name", "console", "warn", "location"], "mappings": ";;;;;;;;AAGO,SAASA,gBAAgBA,GAAG;EACjC,QAEE,OAAOC,OAAO,KAAK,QAAQ,KACxBA,OAAO,CAACC,GAAG,CAACC,WAAW,KAAK,GAAG,IAAIF,OAAO,CAACC,GAAG,CAACC,WAAW,KAAK,OAAO,CAAC,GACtE,KAAK,GACLC,UAAU,CAACJ,gBAAAA;AAAgB,IAAA;AAEnC,CAAA;AAmBA,MAAMK,OAAkE,GACtEA,CAACC,CAAC,EAAEC,CAAC,KAAKC,CAAC,IACTF,CAAC,CAACC,CAAC,CAACC,CAAC,CAAC,CAAC,CAAA;AAKX,SAASC,SAASA,CAACC,MAAc,EAAQ;EACvC,OAAO;IACLC,OAAO,EAAED,MAAM,CAACE,IAAI;IACpBC,WAAW,EAAEH,MAAM,CAACI,MAAM;IAC1BC,aAAa,EAAEL,MAAM,CAACI,MAAM;IAC5BE,UAAU,EAAEN,MAAM,CAACI,MAAM;IACzBG,MAAM,EAAEP,MAAM,CAACQ,OAAO;IACtBC,MAAM,EAAET,MAAM,CAACU,KAAK;IACpBC,KAAK,EAAEX,MAAM,CAACQ,OAAO;IACrBI,OAAO,EAAEZ,MAAM,CAACa,IAAI;AACpBC,IAAAA,OAAO,EAAEnB,OAAO,CAACA,OAAO,CAACK,MAAM,CAACe,KAAK,EAAEf,MAAM,CAACgB,KAAK,CAAC,EAAEhB,MAAM,CAACiB,IAAI,CAAC;IAElEC,MAAM,EAAElB,MAAM,CAACa,IAAI;IACnBM,MAAM,EAAExB,OAAO,CAACK,MAAM,CAACoB,GAAG,EAAEpB,MAAM,CAACiB,IAAI,CAAC;IACxCI,OAAO,EAAE1B,OAAO,CAACK,MAAM,CAACoB,GAAG,EAAEpB,MAAM,CAACiB,IAAI,CAAC;IAEzCK,KAAK,EAAEtB,MAAM,CAACsB,KAAAA;GACf,CAAA;AACH,CAAA;AAEA,MAAMC,MAAM,GAAGxB,SAAS,CAACyB,uBAAY,CAAC,IAAI,CAAC,CAAC,CAAA;AAC5C,MAAMC,OAAO,GAAG1B,SAAS,CAACyB,uBAAY,CAAC,KAAK,CAAC,CAAC,CAAA;AAEvC,SAASE,OAAOA,CAACC,OAAgB,EAAQ;AAC9C,EAAA,OAAOA,OAAO,GAAGJ,MAAM,GAAGE,OAAO,CAAA;AACnC;;AC3CA,MAAMG,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;AAU9E,MAAMC,SAAO,GAAG,yBAAyB,CAAA;AAKzC,MAAMC,OAAO,GAAG,aAAa,CAAA;AAE7B,IAAIC,QAEoE,CAAA;AA6FjE;EAIL,MAAMC,OAAO,GAAG,gBAAgB,CAAA;EAIhC,MAAMC,YAAY,GAAG,UAAUC,KAAU,EAAEC,MAAc,EAAEC,IAAY,EAAE;AACvE,IAAA,IAAIF,KAAK,CAACG,IAAI,KAAK,MAAM,EAAE;MACzB,IACEC,mCAAS,CAACJ,KAAK,CAACK,KAAK,CAAC,IACtBC,8CAAoB,CAACN,KAAK,CAACK,KAAK,EAAE,IAAI,CAAC,IACvCZ,iBAAiB,CAACc,GAAG,CAACP,KAAK,CAACK,KAAK,CAAC,EAClC;AACA,QAAA,OAAO,SAAS,CAAA;AAClB,OAAA;AAEA,MAAA,IACEP,OAAO,CAACU,IAAI,CAACR,KAAK,CAACK,KAAK,CAAC,KACxBH,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,IAAIC,IAAI,CAACO,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC,KAAK,IAAI,CAAC,EACrE;AACA,QAAA,OAAO,eAAe,CAAA;AACxB,OAAA;AAEA,MAAA,IAAID,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,KAAKL,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,CAACK,WAAW,EAAE,EAAE;AACnD,QAAA,OAAO,aAAa,CAAA;AACtB,OAAA;AACF,KAAA;AAEA,IAAA,IAAIV,KAAK,CAACG,IAAI,KAAK,YAAY,IAAIP,OAAO,CAACY,IAAI,CAACR,KAAK,CAACK,KAAK,CAAC,EAAE;AAC5D,MAAA,OAAO,SAAS,CAAA;AAClB,KAAA;AAEA,IAAA,IACEL,KAAK,CAACG,IAAI,KAAK,SAAS,KACvBH,KAAK,CAACK,KAAK,KAAK,GAAG,IAAIL,KAAK,CAACK,KAAK,KAAK,GAAG,CAAC,EAC5C;AACA,MAAA,OAAO,YAAY,CAAA;AACrB,KAAA;IAEA,OAAOL,KAAK,CAACG,IAAI,CAAA;GAClB,CAAA;AAEDN,EAAAA,QAAQ,GAAG,WAAWK,IAAY,EAAE;AAClC,IAAA,IAAIS,KAAK,CAAA;IACT,OAAQA,KAAK,GAAIC,QAAQ,CAASC,OAAO,CAACC,IAAI,CAACZ,IAAI,CAAC,EAAG;AACrD,MAAA,MAAMF,KAAK,GAAIY,QAAQ,CAASG,YAAY,CAACJ,KAAK,CAAC,CAAA;MAEnD,MAAM;QACJR,IAAI,EAAEJ,YAAY,CAACC,KAAK,EAAEW,KAAK,CAACK,KAAK,EAAEd,IAAI,CAAC;QAC5CG,KAAK,EAAEL,KAAK,CAACK,KAAAA;OACd,CAAA;AACH,KAAA;GACD,CAAA;AACH,CAAA;AAEO,SAASY,SAASA,CAACf,IAAY,EAAE;AACtC,EAAA,IAAIA,IAAI,KAAK,EAAE,EAAE,OAAO,EAAE,CAAA;AAE1B,EAAA,MAAMgB,IAAI,GAAG3B,OAAO,CAAC,IAAI,CAAC,CAAA;EAE1B,IAAI4B,WAAW,GAAG,EAAE,CAAA;AAEpB,EAAA,KAAK,MAAM;IAAEhB,IAAI;AAAEE,IAAAA,KAAAA;AAAM,GAAC,IAAIR,QAAQ,CAACK,IAAI,CAAC,EAAE;IAC5C,IAAIC,IAAI,IAAIe,IAAI,EAAE;MAChBC,WAAW,IAAId,KAAK,CACjBe,KAAK,CAACzB,SAAO,CAAC,CACd0B,GAAG,CAACC,GAAG,IAAIJ,IAAI,CAACf,IAAI,CAAsB,CAACmB,GAAG,CAAC,CAAC,CAChDC,IAAI,CAAC,IAAI,CAAC,CAAA;AACf,KAAC,MAAM;AACLJ,MAAAA,WAAW,IAAId,KAAK,CAAA;AACtB,KAAA;AACF,GAAA;AAEA,EAAA,OAAOc,WAAW,CAAA;AACpB;;AC1MA,IAAIK,uBAAuB,GAAG,KAAK,CAAA;AAsCnC,MAAM7B,OAAO,GAAG,yBAAyB,CAAA;AAQzC,SAAS8B,cAAcA,CACrBC,GAAiB,EACjBC,MAAqB,EACrBC,IAAa,EAKb;AACA,EAAA,MAAMC,QAAkB,GAAAC,MAAA,CAAAC,MAAA,CAAA;AACtBC,IAAAA,MAAM,EAAE,CAAC;AACTC,IAAAA,IAAI,EAAE,CAAC,CAAA;GACJP,EAAAA,GAAG,CAACQ,KAAK,CACb,CAAA;EACD,MAAMC,MAAgB,GAAAL,MAAA,CAAAC,MAAA,CACjBF,EAAAA,EAAAA,QAAQ,EACRH,GAAG,CAACU,GAAG,CACX,CAAA;EACD,MAAM;AAAEC,IAAAA,UAAU,GAAG,CAAC;AAAEC,IAAAA,UAAU,GAAG,CAAA;AAAE,GAAC,GAAGV,IAAI,IAAI,EAAE,CAAA;AACrD,EAAA,MAAMW,SAAS,GAAGV,QAAQ,CAACI,IAAI,CAAA;AAC/B,EAAA,MAAMO,WAAW,GAAGX,QAAQ,CAACG,MAAM,CAAA;AACnC,EAAA,MAAMS,OAAO,GAAGN,MAAM,CAACF,IAAI,CAAA;AAC3B,EAAA,MAAMS,SAAS,GAAGP,MAAM,CAACH,MAAM,CAAA;AAE/B,EAAA,IAAIE,KAAK,GAAGS,IAAI,CAACC,GAAG,CAACL,SAAS,IAAIF,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACrD,EAAA,IAAID,GAAG,GAAGO,IAAI,CAACE,GAAG,CAAClB,MAAM,CAACmB,MAAM,EAAEL,OAAO,GAAGH,UAAU,CAAC,CAAA;AAEvD,EAAA,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;AACpBL,IAAAA,KAAK,GAAG,CAAC,CAAA;AACX,GAAA;AAEA,EAAA,IAAIO,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBL,GAAG,GAAGT,MAAM,CAACmB,MAAM,CAAA;AACrB,GAAA;AAEA,EAAA,MAAMC,QAAQ,GAAGN,OAAO,GAAGF,SAAS,CAAA;EACpC,MAAMS,WAAwB,GAAG,EAAE,CAAA;AAEnC,EAAA,IAAID,QAAQ,EAAE;IACZ,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,QAAQ,EAAEE,CAAC,EAAE,EAAE;AAClC,MAAA,MAAMC,UAAU,GAAGD,CAAC,GAAGV,SAAS,CAAA;MAEhC,IAAI,CAACC,WAAW,EAAE;AAChBQ,QAAAA,WAAW,CAACE,UAAU,CAAC,GAAG,IAAI,CAAA;AAChC,OAAC,MAAM,IAAID,CAAC,KAAK,CAAC,EAAE;QAClB,MAAME,YAAY,GAAGxB,MAAM,CAACuB,UAAU,GAAG,CAAC,CAAC,CAACJ,MAAM,CAAA;AAElDE,QAAAA,WAAW,CAACE,UAAU,CAAC,GAAG,CAACV,WAAW,EAAEW,YAAY,GAAGX,WAAW,GAAG,CAAC,CAAC,CAAA;AACzE,OAAC,MAAM,IAAIS,CAAC,KAAKF,QAAQ,EAAE;QACzBC,WAAW,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAER,SAAS,CAAC,CAAA;AAC1C,OAAC,MAAM;QACL,MAAMS,YAAY,GAAGxB,MAAM,CAACuB,UAAU,GAAGD,CAAC,CAAC,CAACH,MAAM,CAAA;QAElDE,WAAW,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAEC,YAAY,CAAC,CAAA;AAC7C,OAAA;AACF,KAAA;AACF,GAAC,MAAM;IACL,IAAIX,WAAW,KAAKE,SAAS,EAAE;AAC7B,MAAA,IAAIF,WAAW,EAAE;QACfQ,WAAW,CAACT,SAAS,CAAC,GAAG,CAACC,WAAW,EAAE,CAAC,CAAC,CAAA;AAC3C,OAAC,MAAM;AACLQ,QAAAA,WAAW,CAACT,SAAS,CAAC,GAAG,IAAI,CAAA;AAC/B,OAAA;AACF,KAAC,MAAM;MACLS,WAAW,CAACT,SAAS,CAAC,GAAG,CAACC,WAAW,EAAEE,SAAS,GAAGF,WAAW,CAAC,CAAA;AACjE,KAAA;AACF,GAAA;EAEA,OAAO;IAAEN,KAAK;IAAEE,GAAG;AAAEY,IAAAA,WAAAA;GAAa,CAAA;AACpC,CAAA;AAEO,SAASI,gBAAgBA,CAC9BC,QAAgB,EAChB3B,GAAiB,EACjBE,IAAa,GAAG,EAAE,EACV;AACR,EAAA,MAAM0B,eAAe,GACnB1B,IAAI,CAAC2B,UAAU,IAAKpG,gBAAgB,EAAE,IAAIyE,IAAI,CAAC4B,aAAc,CAAA;AAC/D,EAAA,MAAMtC,IAAI,GAAG3B,OAAO,CAAC+D,eAAe,CAAC,CAAA;AAErC,EAAA,MAAMG,KAAK,GAAGJ,QAAQ,CAACjC,KAAK,CAACzB,OAAO,CAAC,CAAA;EACrC,MAAM;IAAEuC,KAAK;IAAEE,GAAG;AAAEY,IAAAA,WAAAA;GAAa,GAAGvB,cAAc,CAACC,GAAG,EAAE+B,KAAK,EAAE7B,IAAI,CAAC,CAAA;AACpE,EAAA,MAAM8B,UAAU,GAAGhC,GAAG,CAACQ,KAAK,IAAI,OAAOR,GAAG,CAACQ,KAAK,CAACF,MAAM,KAAK,QAAQ,CAAA;AAEpE,EAAA,MAAM2B,cAAc,GAAGC,MAAM,CAACxB,GAAG,CAAC,CAACU,MAAM,CAAA;EAEzC,MAAMe,gBAAgB,GAAGP,eAAe,GAAGrC,SAAS,CAACoC,QAAQ,CAAC,GAAGA,QAAQ,CAAA;EAEzE,IAAIS,KAAK,GAAGD,gBAAgB,CACzBzC,KAAK,CAACzB,OAAO,EAAEyC,GAAG,CAAC,CACnB3B,KAAK,CAACyB,KAAK,EAAEE,GAAG,CAAC,CACjBf,GAAG,CAAC,CAACY,IAAI,EAAEjB,KAAK,KAAK;AACpB,IAAA,MAAM5C,MAAM,GAAG8D,KAAK,GAAG,CAAC,GAAGlB,KAAK,CAAA;IAChC,MAAM+C,YAAY,GAAG,CAAA,CAAA,EAAI3F,MAAM,CAAA,CAAE,CAACqC,KAAK,CAAC,CAACkD,cAAc,CAAC,CAAA;AACxD,IAAA,MAAM5E,MAAM,GAAG,CAAIgF,CAAAA,EAAAA,YAAY,CAAI,EAAA,CAAA,CAAA;AACnC,IAAA,MAAMC,SAAS,GAAGhB,WAAW,CAAC5E,MAAM,CAAC,CAAA;IACrC,MAAM6F,cAAc,GAAG,CAACjB,WAAW,CAAC5E,MAAM,GAAG,CAAC,CAAC,CAAA;AAC/C,IAAA,IAAI4F,SAAS,EAAE;MACb,IAAIE,UAAU,GAAG,EAAE,CAAA;AACnB,MAAA,IAAIC,KAAK,CAACC,OAAO,CAACJ,SAAS,CAAC,EAAE;AAC5B,QAAA,MAAMK,aAAa,GAAGpC,IAAI,CACvBxB,KAAK,CAAC,CAAC,EAAEkC,IAAI,CAACC,GAAG,CAACoB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CACvCM,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;AACzB,QAAA,MAAMC,eAAe,GAAGP,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;AAEzCE,QAAAA,UAAU,GAAG,CACX,KAAK,EACLhD,IAAI,CAACnC,MAAM,CAACA,MAAM,CAACuF,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EACvC,GAAG,EACHD,aAAa,EACbnD,IAAI,CAAClC,MAAM,CAAC,GAAG,CAAC,CAACwF,MAAM,CAACD,eAAe,CAAC,CACzC,CAAChD,IAAI,CAAC,EAAE,CAAC,CAAA;AAEV,QAAA,IAAI0C,cAAc,IAAIrC,IAAI,CAAC1C,OAAO,EAAE;UAClCgF,UAAU,IAAI,GAAG,GAAGhD,IAAI,CAAChC,OAAO,CAAC0C,IAAI,CAAC1C,OAAO,CAAC,CAAA;AAChD,SAAA;AACF,OAAA;AACA,MAAA,OAAO,CACLgC,IAAI,CAAClC,MAAM,CAAC,GAAG,CAAC,EAChBkC,IAAI,CAACnC,MAAM,CAACA,MAAM,CAAC,EACnBkD,IAAI,CAACa,MAAM,GAAG,CAAC,GAAG,CAAA,CAAA,EAAIb,IAAI,CAAE,CAAA,GAAG,EAAE,EACjCiC,UAAU,CACX,CAAC3C,IAAI,CAAC,EAAE,CAAC,CAAA;AACZ,KAAC,MAAM;AACL,MAAA,OAAO,IAAIL,IAAI,CAACnC,MAAM,CAACA,MAAM,CAAC,CAAGkD,EAAAA,IAAI,CAACa,MAAM,GAAG,CAAC,GAAG,CAAA,CAAA,EAAIb,IAAI,CAAE,CAAA,GAAG,EAAE,CAAE,CAAA,CAAA;AACtE,KAAA;AACF,GAAC,CAAC,CACDV,IAAI,CAAC,IAAI,CAAC,CAAA;AAEb,EAAA,IAAIK,IAAI,CAAC1C,OAAO,IAAI,CAACwE,UAAU,EAAE;AAC/BI,IAAAA,KAAK,GAAG,CAAG,EAAA,GAAG,CAACU,MAAM,CAACb,cAAc,GAAG,CAAC,CAAC,GAAG/B,IAAI,CAAC1C,OAAO,CAAA,EAAA,EAAK4E,KAAK,CAAE,CAAA,CAAA;AACtE,GAAA;AAEA,EAAA,IAAIR,eAAe,EAAE;AACnB,IAAA,OAAOpC,IAAI,CAAC/B,KAAK,CAAC2E,KAAK,CAAC,CAAA;AAC1B,GAAC,MAAM;AACL,IAAA,OAAOA,KAAK,CAAA;AACd,GAAA;AACF,CAAA;AAMe,cAAA,EACbT,QAAgB,EAChBH,UAAkB,EAClBuB,SAAyB,EACzB7C,IAAa,GAAG,EAAE,EACV;EACR,IAAI,CAACJ,uBAAuB,EAAE;AAC5BA,IAAAA,uBAAuB,GAAG,IAAI,CAAA;IAE9B,MAAMtC,OAAO,GACX,qGAAqG,CAAA;IAEvG,IAAI9B,OAAO,CAACsH,WAAW,EAAE;AAGvBtH,MAAAA,OAAO,CAACsH,WAAW,CAACxF,OAAO,EAAE,oBAAoB,CAAC,CAAA;AACpD,KAAC,MAAM;AACL,MAAA,MAAMyF,gBAAgB,GAAG,IAAIC,KAAK,CAAC1F,OAAO,CAAC,CAAA;MAC3CyF,gBAAgB,CAACE,IAAI,GAAG,oBAAoB,CAAA;MAC5CC,OAAO,CAACC,IAAI,CAAC,IAAIH,KAAK,CAAC1F,OAAO,CAAC,CAAC,CAAA;AAClC,KAAA;AACF,GAAA;EAEAuF,SAAS,GAAG9B,IAAI,CAACC,GAAG,CAAC6B,SAAS,EAAE,CAAC,CAAC,CAAA;AAElC,EAAA,MAAMO,QAAsB,GAAG;AAC7B9C,IAAAA,KAAK,EAAE;AAAEF,MAAAA,MAAM,EAAEyC,SAAS;AAAExC,MAAAA,IAAI,EAAEiB,UAAAA;AAAW,KAAA;GAC9C,CAAA;AAED,EAAA,OAAOE,gBAAgB,CAACC,QAAQ,EAAE2B,QAAQ,EAAEpD,IAAI,CAAC,CAAA;AACnD;;;;;;"}