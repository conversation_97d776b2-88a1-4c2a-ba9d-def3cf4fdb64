"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _createSuper;
var _getPrototypeOf = require("getPrototypeOf");
var _isNativeReflectConstruct = require("isNativeReflectConstruct");
var _possibleConstructorReturn = require("possibleConstructorReturn");
function _createSuper(Derived) {
  var hasNativeReflectConstruct = _isNativeReflectConstruct();
  return function _createSuperInternal() {
    var Super = _getPrototypeOf(Derived),
      result;
    if (hasNativeReflectConstruct) {
      var NewTarget = _getPrototypeOf(this).constructor;
      result = Reflect.construct(Super, arguments, NewTarget);
    } else {
      result = Super.apply(this, arguments);
    }
    return _possibleConstructorReturn(this, result);
  };
}

//# sourceMappingURL=createSuper.js.map
